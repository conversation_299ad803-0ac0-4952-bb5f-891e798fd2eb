#include <iostream>
#include <string>
#include <vector>
#include <sstream>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <limits>
#include <Windows.h>
#include <locale>
#include <io.h>
#include <fcntl.h>
#include "fix_encoding.h"

#pragma comment(lib, "ws2_32.lib")

using namespace std;

class ClientUI {
public:
    enum class UserType { Merchant, Consumer, None };

    ClientUI()
        : sock_(INVALID_SOCKET),
          currentUserType_(UserType::None),
          running_(true),
          loggedIn_(false)
    {
        WSADATA wsaData;
        if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
            cerr << "Winsock 初始化失败: " << WSAGetLastError() << endl;
            exit(1);
        }

        sock_ = socket(AF_INET, SOCK_STREAM, 0);
        if (sock_ == INVALID_SOCKET) {
            cerr << "创建 socket 失败: " << WSAGetLastError() << endl;
            WSACleanup();
            exit(1);
        }

        sockaddr_in serv_addr;
        ZeroMemory(&serv_addr, sizeof(serv_addr));
        serv_addr.sin_family = AF_INET;
        serv_addr.sin_port = htons(8080);
        serv_addr.sin_addr.s_addr = inet_addr("127.0.0.1");
        if (serv_addr.sin_addr.s_addr == INADDR_NONE) {
            cerr << "无效的 IP 地址" << endl;
            closesocket(sock_);
            WSACleanup();
            exit(1);
        }

        if (connect(sock_, reinterpret_cast<sockaddr*>(&serv_addr), sizeof(serv_addr)) == SOCKET_ERROR) {
            cerr << "连接失败，错误码: " << WSAGetLastError() << endl;
            cout << "按回车键继续..." << endl;
            cin.ignore(numeric_limits<streamsize>::max(), '\n');
            system("cls");
            closesocket(sock_);
            WSACleanup();
            exit(1);
        }

        cout << "已连接到服务器…" << endl;
    }

    ~ClientUI() {
        if (sock_ != INVALID_SOCKET) {
            closesocket(sock_);
        }
        WSACleanup();
    }

    void run() {
        while (running_) {
            if (!loggedIn_) {
                displayMainMenu();
                int choice;
                cout << "请选择操作: ";
                cin >> choice;
                cin.ignore(numeric_limits<streamsize>::max(), '\n');

                switch (choice) {
                case 1:
                    handleLogin();
                    break;
                case 2:
                    handleRegister();
                    break;
                case 3:
                    handleListProducts();
                    break;
                case 4:
                    handleSearchProducts();
                    break;
                case 0:
                    running_ = false;
                    break;
                default:
                    cout << "无效的选择，请重试。" << endl;
                    break;
                }
            }
            else {
                if (currentUserType_ == UserType::Merchant) {
                    displayMerchantMenu(loggedInUser_);
                    int choice;
                    cout << "请选择操作: ";
                    cin >> choice;
                    cin.ignore(numeric_limits<streamsize>::max(), '\n');

                    switch (choice) {
                    case 1:
                        handleListProducts();
                        break;
                    case 2:
                        handleSearchProducts();
                        break;
                    case 3:
                        handleChangePassword();
                        break;
                    case 4:
                        handleRecharge();
                        break;
                    case 5:
                        handleGetBalance();
                        break;
                    case 6:
                        handleAddProduct();
                        break;
                    case 7:
                        handleUpdateProduct();
                        break;
                    case 8:
                        handleApplyDiscount();
                        break;
                    case 9:
                        handleViewMyProducts();
                        break;
                    case 0:
                        logout();
                        break;
                    default:
                        cout << "无效的选择，请重试。" << endl;
                        break;
                    }
                }
                else if (currentUserType_ == UserType::Consumer) {
                    displayConsumerMenu(loggedInUser_);
                    int choice;
                    cout << "请选择操作: ";
                    cin >> choice;
                    cin.ignore(numeric_limits<streamsize>::max(), '\n');

                    switch (choice) {
                    case 1:
                        handleListProducts();
                        break;
                    case 2:
                        handleSearchProducts();
                        break;
                    case 3:
                        handleChangePassword();
                        break;
                    case 4:
                        handleRecharge();
                        break;
                    case 5:
                        handleGetBalance();
                        break;
                    case 6:
                        handleAddToCart();
                        break;
                    case 7:
                        handleViewCart();
                        break;
                    case 8:
                        handleUpdateCart();
                        break;
                    case 9:
                        handleCreateOrder();
                        break;
                    case 10:
                        handlePayOrder();
                        break;
                    case 11:
                        handleCancelOrder();
                        break;
                    case 12:
                        handleViewOrders();
                        break;
                    case 0:
                        logout();
                        break;
                    default:
                        cout << "无效的选择，请重试。" << endl;
                        break;
                    }
                }
            }
        }

        cout << "客户端已退出" << endl;
    }

private:
    SOCKET sock_; // 用于和服务器通信的 TCP 套接字
    UserType currentUserType_; // 记录当前登录用户身份（商家/消费者/无）
    bool running_; // 控制主循环是否继续运行
    bool loggedIn_; // 标志用户是否已经成功登录
    string loggedInUser_; // 记录已登录的用户名

    // —— 菜单展示 —— //
    void displayMainMenu() {
        cout << "\n============================" << endl;
        cout << "       电商平台·主菜单       " << endl;
        cout << "============================" << endl;
        cout << "1. 登录" << endl;
        cout << "2. 注册" << endl;
        cout << "3. 查看所有商品" << endl;
        cout << "4. 搜索商品" << endl;
        cout << "0. 退出客户端" << endl;
        cout << "============================" << endl;
    }

    void displayMerchantMenu(const string& username) {
        cout << "\n============================" << endl;
        cout << "   商家菜单 - 用户: " << username << "   " << endl;
        cout << "============================" << endl;
        cout << "1. 查看所有商品" << endl;
        cout << "2. 搜索商品" << endl;
        cout << "3. 修改密码" << endl;
        cout << "4. 充值" << endl;
        cout << "5. 查看余额" << endl;
        cout << "6. 添加商品" << endl;
        cout << "7. 更新商品" << endl;
        cout << "8. 设置类别折扣" << endl;
        cout << "9. 查看我的商品" << endl;
        cout << "0. 退出登录" << endl;
        cout << "============================" << endl;
    }

    void displayConsumerMenu(const string& username) {
        cout << "\n============================" << endl;
        cout << "   消费者菜单 - 用户: " << username << "   " << endl;
        cout << "============================" << endl;
        cout << "1. 查看所有商品" << endl;
        cout << "2. 搜索商品" << endl;
        cout << "3. 修改密码" << endl;
        cout << "4. 充值" << endl;
        cout << "5. 查看余额" << endl;
        cout << "6. 添加商品到购物车" << endl;
        cout << "7. 查看购物车" << endl;
        cout << "8. 修改购物车商品数量" << endl;
        cout << "9. 创建订单" << endl;
        cout << "10. 支付订单" << endl;
        cout << "11. 取消订单" << endl;
        cout << "12. 查看订单历史" << endl;
        cout << "0. 退出登录" << endl;
        cout << "============================" << endl;
    }

    // —— 发送命令并接收响应 —— //
    string sendCommand(const string& command) {
        if (sock_ == INVALID_SOCKET) {
            return "错误：未连接服务器";
        }
        int sendLen = static_cast<int>(command.size());
        int sent = send(sock_, command.c_str(), sendLen, 0);
        if (sent == SOCKET_ERROR) {
            return "错误：发送失败";
        }
        char buffer[2048] = {0};
        int bytes_read = recv(sock_, buffer, sizeof(buffer) - 1, 0);
        if (bytes_read <= 0) {
            return "错误：服务器已断开";
        }
        return string(buffer, bytes_read);
    }

    // —— 登录/登出 —— //
    void handleLogin() {
        string username, password;
        cout << "用户名: ";
        getline(cin, username);
        cout << "密码: ";
        getline(cin, password);

        string cmd = "LOGIN " + username + " " + password;
        string resp = sendCommand(cmd);
        if (resp.rfind("200", 0) == 0) {
            // 形如 "200 登录成功 merchant"
            stringstream ss(resp);
            string code, msg, typeStr;
            ss >> code >> msg >> typeStr;
            if (typeStr == "merchant") {
                currentUserType_ = UserType::Merchant;
            }
            else {
                currentUserType_ = UserType::Consumer;
            }
            loggedIn_ = true;
            loggedInUser_ = username;
            cout << "登录成功！用户类型: " << typeStr << endl;
            cout << "按回车键继续..." << endl;
            cin.ignore(numeric_limits<streamsize>::max(), '\n');
            system("cls");
        }
        else {
            cout << "登录失败: " << resp.substr(4) << endl;
        }
    }

    void handleRegister() {
        string username, password, confirm, typeStr;
        cout << "用户名: ";
        getline(cin, username);
        cout << "密码: ";
        getline(cin, password);
        cout << "请确认密码: ";
        getline(cin, confirm);
        if (password != confirm) {
            cout << "两次密码不一致！" << endl;
            return;
        }
        cout << "用户类型(consumer/merchant): ";
        getline(cin, typeStr);
        if (typeStr != "consumer" && typeStr != "merchant") {
            cout << "无效的用户类型！" << endl;
            return;
        }
        string cmd = "REGISTER " + username + " " + password + " " + typeStr;
        string resp = sendCommand(cmd);
        if (resp.rfind("200", 0) == 0) {
            cout << "注册成功！" << endl;
        }
        else {
            cout << "注册失败: " << resp.substr(4) << endl;
        }
    }

    void logout() {
        // 在登出前，先向服务器发送 LOGOUT 命令，让服务器检查未支付订单并解冻库存
        string resp = sendCommand("LOGOUT");
        if (resp.rfind("200", 0) == 0) {
            // 可以输出确认信息，也可以不输出
            // cout << "服务器已清理未支付订单并恢复库存" << endl;
        }
        // 本地清理
        loggedIn_ = false;
        currentUserType_ = UserType::None;
        loggedInUser_.clear();
        cout << "已退出登录，返回主菜单" << endl;
        cout << "按回车键继续..." << endl;
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
        system("cls");
    }

    // —— 商品展示 —— //
    void handleListProducts() {
        string resp = sendCommand("LIST_PRODUCTS");
        parseAndShowProducts(resp);
    }

    void handleSearchProducts() {
        string keyword;
        cout << "输入搜索关键词: ";
        getline(cin, keyword);
        string cmd = "SEARCH " + keyword;
        string resp = sendCommand(cmd);
        parseAndShowProducts(resp);
    }

    // —— 修改密码/充值/余额 —— //
    void handleChangePassword() {
        string oldPwd, newPwd, confirm;
        cout << "请输入旧密码: ";
        getline(cin, oldPwd);
        cout << "请输入新密码: ";
        getline(cin, newPwd);
        cout << "请确认新密码: ";
        getline(cin, confirm);
        if (newPwd != confirm) {
            cout << "两次新密码不一致！" << endl;
            return;
        }
        string cmd = "CHANGE_PASSWORD " + oldPwd + " " + newPwd;
        string resp = sendCommand(cmd);
        if (resp.rfind("200", 0) == 0) {
            cout << "密码修改成功！" << endl;
        }
        else {
            cout << "修改失败: " << resp.substr(4) << endl;
        }
    }

    void handleRecharge() {
        double amount;
        cout << "请输入充值金额: ";
        cin >> amount;
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
        string cmd = "RECHARGE " + to_string(amount);
        string resp = sendCommand(cmd);
        if (resp.rfind("200", 0) == 0) {
            cout << resp.substr(4) << endl;
        }
        else {
            cout << "充值失败: " << resp.substr(4) << endl;
        }
    }

    void handleGetBalance() {
        string resp = sendCommand("GET_BALANCE");
        if (resp.rfind("200", 0) == 0) {
            cout << "当前余额: " << resp.substr(4) << endl;
        }
        else {
            cout << "查询失败: " << resp.substr(4) << endl;
        }
    }

    // —— 商家商品管理 —— //
    void handleAddProduct() {
        cout << "请输入商品名称: ";
        string name; getline(cin, name);
        cout << "请输入商品描述: ";
        string description; getline(cin, description);
        double price;
        cout << "请输入商品价格: ";
        cin >> price; cin.ignore(numeric_limits<streamsize>::max(), '\n');
        int quantity;
        cout << "请输入商品数量: ";
        cin >> quantity; cin.ignore(numeric_limits<streamsize>::max(), '\n');
        cout << "请选择类别: 1.图书 2.食品 3.服装" << endl;
        int category;
        cout << "输入(1-3): ";
        cin >> category; cin.ignore(numeric_limits<streamsize>::max(), '\n');

        string cmd = "ADD_PRODUCT ";
        if (category == 1) {
            string author, publisher;
            cout << "请输入作者: ";
            getline(cin, author);
            cout << "请输入出版社: ";
            getline(cin, publisher);
            cmd += name + "|" + description + "|" + to_string(price) + "|" + to_string(quantity) + "|1|" + author + "|" + publisher;
        }
        else if (category == 2) {
            string prodDate, expDate;
            cout << "请输入生产日期 (YYYY-MM-DD): ";
            getline(cin, prodDate);
            cout << "请输入过期日期 (YYYY-MM-DD): ";
            getline(cin, expDate);
            cmd += name + "|" + description + "|" + to_string(price) + "|" + to_string(quantity) + "|2|" + prodDate + "|" + expDate;
        }
        else if (category == 3) {
            string size, color;
            cout << "请输入尺码: ";
            getline(cin, size);
            cout << "请输入颜色: ";
            getline(cin, color);
            cmd += name + "|" + description + "|" + to_string(price) + "|" + to_string(quantity) + "|3|" + size + "|" + color;
        }
        else {
            cout << "无效的商品类别" << endl;
            return;
        }

        string resp = sendCommand(cmd);
        if (resp.rfind("200", 0) == 0) {
            cout << "商品添加成功！" << endl;
        }
        else {
            cout << "添加失败: " << resp.substr(4) << endl;
        }
    }

    void handleUpdateProduct() {
        cout << "请输入要更新的商品名称: ";
        string name; getline(cin, name);
        cout << "请输入新描述: ";
        string description; getline(cin, description);
        double price;
        cout << "请输入新价格: ";
        cin >> price; cin.ignore(numeric_limits<streamsize>::max(), '\n');
        int quantity;
        cout << "请输入新数量: ";
        cin >> quantity; cin.ignore(numeric_limits<streamsize>::max(), '\n');
        string cmd = "UPDATE_PRODUCT " + name + "|" + description + "|" + to_string(price) + "|" + to_string(quantity);
        string resp = sendCommand(cmd);
        if (resp.rfind("200", 0) == 0) {
            cout << "商品更新成功！" << endl;
        }
        else {
            cout << "更新失败: " << resp.substr(4) << endl;
        }
    }

    void handleApplyDiscount() {
        cout << "请选择类别: 1.图书 2.食品 3.服装" << endl;
        int category;
        cout << "输入(1-3): ";
        cin >> category; cin.ignore(numeric_limits<streamsize>::max(), '\n');
        double rate;
        cout << "请输入折扣率 (0~1): ";
        cin >> rate; cin.ignore(numeric_limits<streamsize>::max(), '\n');
        string cmd = "APPLY_DISCOUNT " + to_string(category) + "|" + to_string(rate);
        string resp = sendCommand(cmd);
        if (resp.rfind("200", 0) == 0) {
            cout << "折扣设置成功！" << endl;
        }
        else {
            cout << "设置失败: " << resp.substr(4) << endl;
        }
    }

    void handleViewMyProducts() {
        string resp = sendCommand("VIEW_MY_PRODUCTS");
        if (resp.rfind("200", 0) == 0) {
            parseAndShowProducts(resp);
        }
        else {
            cout << "查询失败: " << resp.substr(4) << endl;
        }
    }

    // —— 购物车与订单 —— //
    void handleAddToCart() {
        string name;
        int qty;
        cout << "商品名称（必须完整匹配）: ";
        getline(cin, name);
        cout << "数量: ";
        cin >> qty; cin.ignore(numeric_limits<streamsize>::max(), '\n');
        string cmd = "ADD_TO_CART " + name + "|" + to_string(qty);
        string resp = sendCommand(cmd);
        if (resp.rfind("200", 0) == 0) {
            cout << "商品已加入购物车" << endl;
        }
        else {
            cout << "添加失败: " << resp.substr(4) << endl;
        }
    }

    void handleViewCart() {
        string resp = sendCommand("VIEW_CART");
        parseAndShowCart(resp);
    }

    void handleUpdateCart() {
        string name;
        int newQty;
        cout << "商品名称（必须完整匹配）: ";
        getline(cin, name);
        cout << "新数量(0则移除): ";
        cin >> newQty; cin.ignore(numeric_limits<streamsize>::max(), '\n');
        string cmd = "UPDATE_CART " + name + "|" + to_string(newQty);
        string resp = sendCommand(cmd);
        if (resp.rfind("200", 0) == 0) {
            cout << "购物车已更新" << endl;
        }
        else {
            cout << "更新失败: " << resp.substr(4) << endl;
        }
    }

    void handleCreateOrder() {
        string respCart = sendCommand("VIEW_CART");
        parseAndShowCart(respCart);

        cout << "是否要将购物车中所有商品全部下单？(y/n): ";
        char yn;
        cin >> yn; cin.ignore(numeric_limits<streamsize>::max(), '\n');
        if (yn == 'y' || yn == 'Y') {
            string resp = sendCommand("CREATE_ORDER");
            if (resp.rfind("200", 0) == 0) {
                cout << resp.substr(4) << endl;
            }
            else {
                cout << "创建失败: " << resp.substr(4) << endl;
            }
        }
        else {
            cout << "请输入要下单的商品名称（逗号分隔，不含空格），如 book1,food2,clothing3: ";
            string namesLine;
            getline(cin, namesLine);
            string cmd = "CREATE_ORDER " + namesLine;
            string resp = sendCommand(cmd);
            if (resp.rfind("200", 0) == 0) {
                cout << resp.substr(4) << endl;
            }
            else {
                cout << "创建失败: " << resp.substr(4) << endl;
            }
        }
    }

    void handleViewOrders() {
        string resp = sendCommand("VIEW_ORDERS");
        parseAndShowOrders(resp);
    }

    void handlePayOrder() {
        string respOrders = sendCommand("VIEW_ORDERS");
        parseAndShowOrders(respOrders);

        cout << "请输入要支付的订单号: ";
        int orderId;
        cin >> orderId; cin.ignore(numeric_limits<streamsize>::max(), '\n');
        string cmd = "PAY_ORDER " + to_string(orderId);
        string resp = sendCommand(cmd);
        if (resp.rfind("200", 0) == 0) {
            cout << "支付成功: " << resp.substr(4) << endl;
        }
        else {
            cout << "支付失败: " << resp.substr(4) << endl;
        }
    }

    void handleCancelOrder() {
        string respOrders = sendCommand("VIEW_ORDERS");
        parseAndShowOrders(respOrders);

        cout << "请输入要取消的订单号: ";
        int orderId;
        cin >> orderId; cin.ignore(numeric_limits<streamsize>::max(), '\n');
        string cmd = "CANCEL_ORDER " + to_string(orderId);
        string resp = sendCommand(cmd);
        if (resp.rfind("200", 0) == 0) {
            cout << "取消成功: " << resp.substr(4) << endl;
        }
        else {
            cout << "取消失败: " << resp.substr(4) << endl;
        }
    }

    // —— 解析并展示响应 —— //
    void parseAndShowProducts(const string& response) {
        if (response.size() < 4) {
            cout << "响应格式错误" << endl;
            return;
        }
        if (response.substr(0, 3) != "200") {
            cout << "错误: " << response.substr(4) << endl;
            return;
        }
        string data = response.substr(4);
        if (data.empty()) {
            cout << "没有商品" << endl;
            return;
        }
        vector<string> items;
        stringstream ss(data);
        string item;
        while (getline(ss, item, ';')) {
            if (!item.empty()) items.push_back(item);
        }
        cout << "\n—— 商品列表 ——————————————————" << endl;
        for (const auto& p : items) {
            vector<string> fields;
            stringstream pss(p);
            string field;
            while (getline(pss, field, '|')) {
                fields.push_back(field);
            }
            if (fields.size() >= 5) {
                cout << "名称: " << fields[0] << endl;
                cout << "描述: " << fields[1] << endl;
                cout << "价格: " << fields[2] << endl;
                cout << "库存: " << fields[3] << endl;
                cout << "类别: " << fields[4] << endl;
                cout << "----------------------------------" << endl;
            }
        }
    }

    void parseAndShowCart(const string& response) {
        if (response.size() < 4) {
            cout << "响应格式错误" << endl;
            return;
        }
        if (response.substr(0, 3) != "200") {
            cout << "错误: " << response.substr(4) << endl;
            return;
        }
        string data = response.substr(4);
        if (data.empty()) {
            cout << "购物车为空" << endl;
            return;
        }
        vector<string> items;
        stringstream ss(data);
        string item;
        while (getline(ss, item, ';')) {
            if (!item.empty()) items.push_back(item);
        }
        cout << "\n—— 购物车内容 ——————————————————" << endl;
        for (const auto& it : items) {
            vector<string> fields;
            stringstream iss(it);
            string field;
            while (getline(iss, field, '|')) {
                fields.push_back(field);
            }
            if (fields.size() >= 2) {
                cout << "商品: " << fields[0] << " × " << fields[1] << endl;
            }
        }
        cout << "----------------------------------" << endl;
    }

    void parseAndShowOrders(const string& response) {
        if (response.size() < 4) {
            cout << "响应格式错误" << endl;
            return;
        }
        if (response.substr(0, 3) != "200") {
            cout << "错误: " << response.substr(4) << endl;
            return;
        }
        string data = response.substr(4);
        if (data.empty()) {
            cout << "没有订单" << endl;
            return;
        }
        vector<string> orders;
        stringstream ss(data);
        string ord;
        while (getline(ss, ord, ';')) {
            if (!ord.empty()) orders.push_back(ord);
        }
        cout << "\n—— 订单列表 ——————————————————" << endl;
        for (const auto& o : orders) {
            vector<string> fields;
            stringstream oss(o);
            string field;
            while (getline(oss, field, '|')) {
                fields.push_back(field);
            }
            if (fields.size() >= 3) {
                cout << "订单号: " << fields[0] << endl;
                cout << "总金额: " << fields[1] << endl;
                cout << "状态: " << fields[2] << endl;
                cout << "----------------------------------" << endl;
            }
        }
    }
};

int main() {
    // 修复编码问题
    EncodingFixer::fixConsoleEncoding();
    EncodingFixer::printWelcome();

    try {
        ClientUI app;
        app.run();
    }
    catch (const exception& e) {
        cerr << "客户端错误: " << e.what() << endl;
        cout << "按任意键退出..." << endl;
        cin.get();
        return 1;
    }

    return 0;
}
