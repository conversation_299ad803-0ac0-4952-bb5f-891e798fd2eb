{"version": "0.2.0", "configurations": [{"name": "启动服务端", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/server.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": true, "MIMode": "gdb", "miDebuggerPath": "gdb.exe", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "编译服务端"}, {"name": "启动客户端", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/client.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": true, "MIMode": "gdb", "miDebuggerPath": "gdb.exe", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "编译客户端"}], "compounds": [{"name": "服务端/客户端", "configurations": ["启动服务端", "启动客户端"]}]}