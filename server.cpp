#include <winsock2.h>     // 必须在 windows.h 之前包含
#include <windows.h>
#include <ws2tcpip.h>
#include <iostream>
#include <sstream>
#include <fstream>
#include <string>
#include <vector>
#include <algorithm>
#include <stdexcept>
#include <map>
#include <memory>
#include <limits>
#include <atomic>
#include <unordered_map>
#include <locale>
#include <io.h>
#include <fcntl.h>
#include "fix_encoding.h"

#pragma comment(lib, "ws2_32.lib")

using namespace std;

// —— 异常类定义 —— //

class FileException : public exception {
private:
    string message;
public:
    FileException(const string& msg) : message(msg) {}
    const char* what() const noexcept override {
        return message.c_str();
    }
};

class InputException : public exception {
private:
    string message;
public:
    InputException(const string& msg) : message(msg) {}
    const char* what() const noexcept override {
        return message.c_str();
    }
};

class MerchantException : public exception {
private:
    string message;
public:
    MerchantException(const string& msg) : message(msg) {}
    const char* what() const noexcept override {
        return message.c_str();
    }
};

class OrderException : public exception {
private:
    string message;
public:
    OrderException(const string& msg) : message(msg) {}
    const char* what() const noexcept override {
        return message.c_str();
    }
};

// —— 枚举类型 —— //

enum class UserType { merchant = 0, consumer = 1 };
enum class ProductType { book = 0, food = 1, clothing = 2 };

// —— 用户类 —— //

class User {
protected:
    string username;
    string password;
    double balance;
    UserType utype;

public:
    User(const string& uname, const string& pwd, double bal, UserType ut)
        : username(uname), password(pwd), balance(bal), utype(ut) {}
    virtual ~User() = default;

    string getUsername() const { return username; }
    string getPassword() const { return password; }
    double getBalance() const { return balance; }

    void setUsername(const string& uname) { username = uname; }
    void setPassword(const string& pwd) { password = pwd; }
    void setBalance(double bal) { balance = bal; }

    virtual UserType getUserType() const = 0;

    void recharge(double amount) {
        if (amount <= 0) {
            throw InputException("充值金额必须大于0");
        }
        balance += amount;
    }

    void consume(double amount) {
        if (amount <= 0) {
            throw InputException("消费金额必须大于0");
        }
        if (balance < amount) {
            throw InputException("余额不足");
        }
        balance -= amount;
    }
};

class Consumer : public User {
public:
    Consumer(const string& uname, const string& pwd, double bal = 0.0)
        : User(uname, pwd, bal, UserType::consumer) {}
    UserType getUserType() const override {
        return UserType::consumer;
    }
};

class Merchant : public User {
public:
    Merchant(const string& uname, const string& pwd, double bal = 0.0)
        : User(uname, pwd, bal, UserType::merchant) {}
    UserType getUserType() const override {
        return UserType::merchant;
    }
};

// —— 商品类 —— //

class Product {
protected:
    string name;
    string description;
    double originalPrice;
    int remainingQuantity;
    double discountRate;
    ProductType ptype;
    string merchantUsername;

public:
    Product(const string& n, const string& desc, double price, int quantity, ProductType pt, const string& merchant)
        : name(n), description(desc), originalPrice(price),
          remainingQuantity(quantity), discountRate(1.0),
          ptype(pt), merchantUsername(merchant) {}
    virtual ~Product() = default;

    string getMerchantUsername() const { return merchantUsername; }
    string getName() const { return name; }
    string getDescription() const { return description; }
    double getOriginalPrice() const { return originalPrice; }
    int getRemainingQuantity() const { return remainingQuantity; }
    virtual double getPrice() const {
        return originalPrice * discountRate;
    }

    void setDescription(const string& desc) { description = desc; }
    void setOriginalPrice(double price) { originalPrice = price; }
    void setRemainingQuantity(int quantity) { remainingQuantity = quantity; }
    void setDiscountRate(double rate) { discountRate = rate; }

    virtual ProductType getCategory() const = 0;
    void applyDiscount(double rate) {
        if (rate <= 0 || rate > 1) {
            throw InputException("折扣率必须在0到1之间");
        }
        discountRate = rate;
    }
    virtual void getFeature() const = 0;

    string getCategoryString() const {
        switch (ptype) {
            case ProductType::book:     return "图书";
            case ProductType::food:     return "食品";
            case ProductType::clothing: return "服装";
            default:                    return "未知";
        }
    }
};

class Book : public Product {
private:
    string author;
    string publisher;

public:
    Book(const string& n, const string& desc, double price, int quantity,
         const string& auth, const string& pub, const string& merchant)
        : Product(n, desc, price, quantity, ProductType::book, merchant),
          author(auth), publisher(pub) {}

    ProductType getCategory() const override {
        return ProductType::book;
    }
    string getAuthor() const { return author; }
    string getPublisher() const { return publisher; }
    void setAuthor(const string& au) { author = au; }
    void setPublisher(const string& pub) { publisher = pub; }

    void getFeature() const override {
        cout << ", 作者: " << author << ", 出版社: " << publisher;
    }
};

class Food : public Product {
private:
    string productionDate;
    string expirationDate;

public:
    Food(const string& n, const string& desc, double price, int quantity,
         const string& prodDate, const string& expDate, const string& merchant)
        : Product(n, desc, price, quantity, ProductType::food, merchant),
          productionDate(prodDate), expirationDate(expDate) {}

    ProductType getCategory() const override {
        return ProductType::food;
    }
    string getProductionDate() const { return productionDate; }
    string getExpirationDate() const { return expirationDate; }
    void setProductionDate(const string& pd) { productionDate = pd; }
    void setExpirationDate(const string& ed) { expirationDate = ed; }

    void getFeature() const override {
        cout << ", 生产日期: " << productionDate << ", 有效日期: " << expirationDate;
    }
};

class Clothing : public Product {
private:
    string size;
    string color;

public:
    Clothing(const string& n, const string& desc, double price, int quantity,
             const string& sz, const string& clr, const string& merchant)
        : Product(n, desc, price, quantity, ProductType::clothing, merchant),
          size(sz), color(clr) {}

    ProductType getCategory() const override {
        return ProductType::clothing;
    }
    string getSize() const { return size; }
    string getColor() const { return color; }
    void setSize(const string& sz) { size = sz; }
    void setColor(const string& cr) { color = cr; }

    void getFeature() const override {
        cout << ", 尺码: " << size << ", 颜色: " << color;
    }
};

// —— 购物车 —— //

class ShoppingCart {
private:
    // map<产品指针, 数量>
    map<Product*, int> items;

public:
    // 添加商品到购物车
    void addItem(Product* product, int quantity) {
        if (quantity <= 0) throw InputException("商品数量必须大于0");
        items[product] += quantity;
    }

    // 移除购物车中某个产品（全部或部分数量）
    void removeItem(Product* product, int quantity) {
        auto it = items.find(product);
        if (it != items.end()) {
            if (quantity >= it->second) {
                items.erase(it);
            } else {
                it->second -= quantity;
            }
        }
    }

    // 更新购物车中某个产品的数量；若 newQty<=0 就直接移除
    void updateQuantity(Product* product, int newQty) {
        if (newQty <= 0) {
            items.erase(product);
        } else {
            items[product] = newQty;
        }
    }
    // 清空购物车
    void clear() {
        items.clear();
    }
    // 获取当前购物车里所有商品及其数量
    const map<Product*, int>& getItems() const { return items; }
    // 判断购物车是否为空
    bool empty() const { return items.empty(); }
};

// —— 订单 —— //

class Order {
private:
    static atomic<int> nextId; // 全局自增订单号
    int orderId;
    map<Product*, int> items;
    double total;  // 订单总金额（下单时按当时折扣计算并冻结库存）
    bool paid;     // 是否支付
    bool cancelled;// 是否取消

public:
    // 构造时会检查库存并冻结数量
    Order(const map<Product*, int>& orderItems)
        : orderId(nextId++), items(orderItems), total(0), paid(false), cancelled(false) {
        // 验证库存
        for (const auto& [product, qty] : items) {
            if (product->getRemainingQuantity() < qty) {
                throw OrderException("商品[" + product->getName() + "]库存不足");
            }
            total += product->getPrice() * qty;
        }
        // 修改商品剩余数量（冻结库存）
        for (auto& [product, qty] : items) {
            product->setRemainingQuantity(product->getRemainingQuantity() - qty);
        }
    }

    int getId() const { return orderId; }
    double getTotal() const { return total; }
    bool isPaid() const { return paid; }
    bool isCancelled() const { return cancelled; }
    const map<Product*, int>& getItems() const { return items; }

    bool pay() {
        if (paid) {
            throw OrderException("订单已支付");
        }
        if (cancelled) {
            throw OrderException("订单已取消");
        }
        paid = true;
        return true;
    }

    void cancel() {
        if (paid) {
            throw OrderException("已支付订单不能取消");
        }
        if (cancelled) {
            throw OrderException("订单已取消");
        }
        // 恢复库存
        for (auto& [product, qty] : items) {
            product->setRemainingQuantity(product->getRemainingQuantity() + qty);
        }
        cancelled = true;
    }
};
atomic<int> Order::nextId(1);

// —— 临界区封装 —— //

class CriticalSectionGuard {
private:
    CRITICAL_SECTION* cs;
public:
    explicit CriticalSectionGuard(CRITICAL_SECTION* criticalSection) : cs(criticalSection) {
        EnterCriticalSection(cs);
    }
    ~CriticalSectionGuard() {
        LeaveCriticalSection(cs);
    }
    CriticalSectionGuard(const CriticalSectionGuard&) = delete;
    CriticalSectionGuard& operator=(const CriticalSectionGuard&) = delete;
};

// —— 用户管理 —— //

class UserManager {
private:
    map<string, unique_ptr<User>> users;
    string userFile;
    mutable CRITICAL_SECTION cs;

    /**
     * @brief 从文件加载用户数据
     * 
     * 从指定的文件中读取用户信息并创建相应的用户对象
     * 文件格式：username,password,balance,userType
     * userType: 0=消费者, 1=商家
     * 使用临界区保护，确保线程安全
     */

    void loadUsers() {
        CriticalSectionGuard guard(&cs);
        ifstream file(userFile);
        if (!file.is_open()) {
            throw FileException("无法打开用户文件: " + userFile);
        }
        string line;
        while (getline(file, line)) {
            if (line.empty()) continue;
            stringstream ss(line);
            string username, password;
            double balance;
            int utypeInt;
            getline(ss, username, ',');
            getline(ss, password, ',');
            ss >> balance;
            ss.ignore();
            ss >> utypeInt;
            if (utypeInt == 1) {
                users[username] = make_unique<Consumer>(username, password, balance);
            } else {
                users[username] = make_unique<Merchant>(username, password, balance);
            }
        }
        file.close();
    }

public:
    /**
     * @brief 构造函数
     * @param filename 用户数据文件路径
     * 
     * 初始化临界区并尝试加载用户数据
     * 如果文件不存在，则创建一个空文件
     */
    UserManager(const string& filename) : userFile(filename) {
        InitializeCriticalSection(&cs);
        try {
            loadUsers();
        } catch (const FileException&) {
            // 文件不存在则创建一个空文件
            ofstream file(userFile);
            if (!file.is_open()) {
                throw FileException("无法创建用户文件: " + userFile);
            }
            file.close();
        }
    }

    ~UserManager() {
        DeleteCriticalSection(&cs);
    }
    /**
     * @brief 构造函数
     * @param filename 用户数据文件路径
     * 
     * 初始化临界区并尝试加载用户数据
     * 如果文件不存在，则创建一个空文件
     */
    void saveUsers() const {
        CriticalSectionGuard guard(&cs);
        ofstream file(userFile);
        if (!file.is_open()) {
            throw FileException("无法保存用户文件: " + userFile);
        }
        for (const auto& pair : users) {
            file << pair.second->getUsername() << ","
                 << pair.second->getPassword() << ","
                 << pair.second->getBalance() << ","
                 << static_cast<int>(pair.second->getUserType()) << "\n";
        }
        file.close();
    }

    User* login(const string& username, const string& password) {
        CriticalSectionGuard guard(&cs);
        auto it = users.find(username);
        if (it == users.end()) {
            throw InputException("用户名不存在");
        }
        if (it->second->getPassword() != password) {
            throw InputException("密码错误");
        }
        return it->second.get();
    }

    void registerUser(const string& username, const string& password, UserType type) {
        CriticalSectionGuard guard(&cs);
        if (users.find(username) != users.end()) {
            throw InputException("用户名已存在");
        }
        if (type == UserType::consumer) {
            users[username] = make_unique<Consumer>(username, password, 0.0);
        } else {
            users[username] = make_unique<Merchant>(username, password, 0.0);
        }
        saveUsers();
    }

    void changePassword(const string& username, const string& oldPwd, const string& newPwd) {
        CriticalSectionGuard guard(&cs);
        auto it = users.find(username);
        if (it == users.end()) {
            throw InputException("用户名不存在");
        }
        if (it->second->getPassword() != oldPwd) {
            throw InputException("旧密码错误");
        }
        it->second->setPassword(newPwd);
        saveUsers();
    }

    User* getMerchant(const string& username) {
        CriticalSectionGuard guard(&cs);
        auto it = users.find(username);
        if (it == users.end()) {
            throw MerchantException("商家不存在");
        }
        if (it->second->getUserType() != UserType::merchant) {
            throw MerchantException("该用户不是商家");
        }
        return it->second.get();
    }
};

// —— 商品管理 —— //

class ProductManager {
private:
    vector<unique_ptr<Product>> products;
    string productFile;
    mutable CRITICAL_SECTION cs;

    /**
     * @brief 构造函数
     * @param filename 用户数据文件路径
     * 
     * 初始化临界区并尝试加载用户数据
     * 如果文件不存在，则创建一个空文件
     */

    void loadProducts() {
        CriticalSectionGuard guard(&cs);
        ifstream file(productFile);
        if (!file.is_open()) {
            throw FileException("无法打开商品文件: " + productFile);
        }
        string line;
        while (getline(file, line)) {
            if (line.empty()) continue;
            stringstream ss(line);
            string name, description, merchant;
            double price;
            int quantity;
            double discountRate;
            int ptypeInt;
            getline(ss, name, ',');
            getline(ss, description, ',');
            ss >> price; ss.ignore();
            ss >> quantity; ss.ignore();
            ss >> discountRate; ss.ignore();
            ss >> ptypeInt; ss.ignore();
            getline(ss, merchant, ',');
            if (ptypeInt == 0) {  // Book
                string author, publisher;
                getline(ss, author, ',');
                getline(ss, publisher);
                auto product = make_unique<Book>(name, description, price, quantity, author, publisher, merchant);
                product->setDiscountRate(discountRate);
                products.push_back(move(product));
            } else if (ptypeInt == 1) {  // Food
                string prodDate, expDate;
                getline(ss, prodDate, ',');
                getline(ss, expDate);
                auto product = make_unique<Food>(name, description, price, quantity, prodDate, expDate, merchant);
                product->setDiscountRate(discountRate);
                products.push_back(move(product));
            } else {  // Clothing
                string size, color;
                getline(ss, size, ',');
                getline(ss, color);
                auto product = make_unique<Clothing>(name, description, price, quantity, size, color, merchant);
                product->setDiscountRate(discountRate);
                products.push_back(move(product));
            }
        }
        file.close();
    }

public:
    ProductManager(const string& filename) : productFile(filename) {
        InitializeCriticalSection(&cs);
        try {
            loadProducts();
        } catch (const FileException&) {
            ofstream file(productFile);
            if (!file.is_open()) {
                throw FileException("无法创建商品文件: " + productFile);
            }
            file.close();
        }
    }

    ~ProductManager() {
        DeleteCriticalSection(&cs);
    }

    void saveProducts() {
        CriticalSectionGuard guard(&cs);
        ofstream file(productFile);
        if (!file.is_open()) {
            throw FileException("无法保存商品文件: " + productFile);
        }
        for (const auto& product : products) {
            file << product->getName() << ","
                 << product->getDescription() << ","
                 << product->getOriginalPrice() << ","
                 << product->getRemainingQuantity() << ","
                 << (product->getPrice() / product->getOriginalPrice()) << ","
                 << static_cast<int>(product->getCategory()) << ","
                 << product->getMerchantUsername() << ",";
            if (auto book = dynamic_cast<Book*>(product.get())) {
                file << book->getAuthor() << "," << book->getPublisher();
            } else if (auto food = dynamic_cast<Food*>(product.get())) {
                file << food->getProductionDate() << "," << food->getExpirationDate();
            } else if (auto clothing = dynamic_cast<Clothing*>(product.get())) {
                file << clothing->getSize() << "," << clothing->getColor();
            }
            file << "\n";
        }
        file.close();
    }

    void addProduct(unique_ptr<Product> product) {
        CriticalSectionGuard guard(&cs);
        products.push_back(move(product));
        saveProducts();
    }

    // 更新指定商家的某一商品信息
    void updateProduct(const string& name, const string& merchantUsername,
                       const string& description, double price, int quantity) {
        CriticalSectionGuard guard(&cs);
        for (auto& product : products) {
            if (product->getName() == name && product->getMerchantUsername() == merchantUsername) {
                product->setDescription(description);
                product->setOriginalPrice(price);
                product->setRemainingQuantity(quantity);
                saveProducts();
                return;
            }
        }
        throw InputException("商品不存在或您无权修改");
    }

    vector<Product*> searchProducts(const string& keyword) {
        CriticalSectionGuard guard(&cs);
        vector<Product*> result;
        for (const auto& product : products) {
            if (product->getName().find(keyword) != string::npos ||
                product->getDescription().find(keyword) != string::npos) {
                result.push_back(product.get());
            }
        }
        return result;
    }

    void applyCategoryDiscount(ProductType category, double discountRate) {
        CriticalSectionGuard guard(&cs);
        for (auto& product : products) {
            if (product->getCategory() == category) {
                product->applyDiscount(discountRate);
            }
        }
        saveProducts();
    }

    const vector<unique_ptr<Product>>& getAllProducts() const {
        return products;
    }
};

// —— 客户端会话状态 —— //

struct ClientSession {
    User* currentUser = nullptr;
    ShoppingCart cart;
    vector<unique_ptr<Order>> orders;
};

// —— 服务端主类 —— //

class ECommerceServer {
private:
    UserManager userManager;
    ProductManager productManager;
    unordered_map<int, ClientSession> sessions;
    CRITICAL_SECTION sessionCs;
    // 辅助类型：线程参数封装
    struct ThreadParam {
        ECommerceServer* server;
        SOCKET client_socket;
    };

    // 静态线程回调函数：为每个连接到来的客户端创建单独的线程
    static DWORD WINAPI ClientThreadProc(LPVOID lpParam) {
        ThreadParam* param = static_cast<ThreadParam*>(lpParam);
        param->server->handleClient(param->client_socket);
        delete param;
        return 0;
    }
    // 根据 clientId 获取或创建会话
    ClientSession& getSession(int clientId) {
        CriticalSectionGuard guard(&sessionCs);
        return sessions[clientId];
    }

    // 处理客户端发来的单条命令
    string processCommand(int clientId, const string& command) {
        stringstream ss(command);
        string cmd;
        ss >> cmd;

        try {
            // —— 用户相关 —— //

            if (cmd == "LOGIN") {
                string username, password;
                ss >> username >> password;

                // ===== 新增：防止重复登录 =====
                {
                    CriticalSectionGuard guard(&sessionCs);
                    for (auto& [otherId, otherSession] : sessions) {
                        if (otherId != clientId
                            && otherSession.currentUser
                            && otherSession.currentUser->getUsername() == username) {
                            throw InputException("该用户已在其他客户端登录");
                        }
                    }
                }
                // ===== 检查通过后再进行实际登录 =====
                auto& session = getSession(clientId);
                session.currentUser = userManager.login(username, password);

                // 返回登录状态及用户类型
                string typeStr = (session.currentUser->getUserType() == UserType::merchant)
                                 ? "merchant" : "consumer";
                return "200 登录成功 " + typeStr;
            }
            else if (cmd == "REGISTER") {
                string username, password, typeStr;
                ss >> username >> password >> typeStr;
                UserType type = (typeStr == "merchant") ? UserType::merchant : UserType::consumer;
                userManager.registerUser(username, password, type);
                return "200 注册成功";
            }
            else if (cmd == "CHANGE_PASSWORD") {
                auto& session = getSession(clientId);
                if (!session.currentUser) throw InputException("请先登录");
                string oldPwd, newPwd;
                ss >> oldPwd >> newPwd;
                userManager.changePassword(session.currentUser->getUsername(), oldPwd, newPwd);
                return "200 密码修改成功";
            }
            else if (cmd == "RECHARGE") {
                auto& session = getSession(clientId);
                if (!session.currentUser) throw InputException("请先登录");
                double amount;
                ss >> amount;
                session.currentUser->recharge(amount);
                userManager.saveUsers();
                return "200 充值成功 当前余额:" + to_string(session.currentUser->getBalance());
            }
            else if (cmd == "GET_BALANCE") {
                auto& session = getSession(clientId);
                if (!session.currentUser) throw InputException("请先登录"); 
                return "200 " + to_string(session.currentUser->getBalance());
            }

            // —— 商品浏览与搜索 —— //

            else if (cmd == "LIST_PRODUCTS") {
                const auto& products = productManager.getAllProducts();
                stringstream result;
                result << "200 ";
                for (const auto& product : products) {
                    result << product->getName() << "|"
                           << product->getDescription() << "|"
                           << product->getPrice() << "|"
                           << product->getRemainingQuantity() << "|"
                           << product->getCategoryString() << ";";
                }
                return result.str();
            }
            else if (cmd == "SEARCH") {
                string keyword;
                getline(ss, keyword);                      // 取到搜索关键字（去掉前导空格）
                if (!keyword.empty() && keyword[0] == ' ')
                    keyword.erase(0, keyword.find_first_not_of(" "));
                auto results = productManager.searchProducts(keyword);
                stringstream result;
                result << "200 ";
                for (const auto& product : results) {
                    result << product->getName() << "|"
                           << product->getDescription() << "|"
                           << product->getPrice() << "|"
                           << product->getRemainingQuantity() << "|"
                           << product->getCategoryString() << ";";
                }
                return result.str();
            }

            // —— 商家专属 —— //

            else if (cmd == "ADD_PRODUCT") {
                auto& session = getSession(clientId);
                if (!session.currentUser) throw InputException("请先登录");
                if (session.currentUser->getUserType() != UserType::merchant) {
                    throw InputException("只有商家可以添加商品");
                }
                // 格式：ADD_PRODUCT name|description|price|quantity|categoryNum|extra1|extra2
                string rest;
                getline(ss, rest);
                if (!rest.empty() && rest[0] == ' ')
                    rest.erase(0, rest.find_first_not_of(" "));
                vector<string> tokens;
                stringstream ts(rest);
                string token;
                while (getline(ts, token, '|')) {
                    tokens.push_back(token);
                }
                if (tokens.size() < 6) {
                    throw InputException("参数不足，添加失败");
                }
                string name = tokens[0];
                string description = tokens[1];
                double price = stod(tokens[2]);
                int quantity = stoi(tokens[3]);
                int categoryNum = stoi(tokens[4]);
                string merchant = session.currentUser->getUsername();

                if (categoryNum == 1) { // 图书
                    if (tokens.size() < 7) {
                        throw InputException("缺少图书参数");
                    }
                    string author = tokens[5];
                    string publisher = tokens[6];
                    auto product = make_unique<Book>(name, description, price, quantity, author, publisher, merchant);
                    productManager.addProduct(move(product));
                }
                else if (categoryNum == 2) { // 食品
                    if (tokens.size() < 7) {
                        throw InputException("缺少食品参数");
                    }
                    string prodDate = tokens[5];
                    string expDate = tokens[6];
                    auto product = make_unique<Food>(name, description, price, quantity, prodDate, expDate, merchant);
                    productManager.addProduct(move(product));
                }
                else if (categoryNum == 3) { // 服装
                    if (tokens.size() < 7) {
                        throw InputException("缺少服装参数");
                    }
                    string size = tokens[5];
                    string color = tokens[6];
                    auto product = make_unique<Clothing>(name, description, price, quantity, size, color, merchant);
                    productManager.addProduct(move(product));
                } else {
                    throw InputException("无效的商品类别");
                }
                return "200 商品添加成功";
            }
            else if (cmd == "UPDATE_PRODUCT") {
                auto& session = getSession(clientId);
                if (!session.currentUser) throw InputException("请先登录");
                if (session.currentUser->getUserType() != UserType::merchant) {
                    throw InputException("只有商家可以更新商品");
                }
                // 格式：UPDATE_PRODUCT name|description|price|quantity
                string rest;
                getline(ss, rest);
                if (!rest.empty() && rest[0] == ' ')
                    rest.erase(0, rest.find_first_not_of(" "));
                vector<string> tokens;
                stringstream ts(rest);
                string token;
                while (getline(ts, token, '|')) {
                    tokens.push_back(token);
                }
                if (tokens.size() < 4) {
                    throw InputException("参数不足，更新失败");
                }
                string name = tokens[0];
                string description = tokens[1];
                double price = stod(tokens[2]);
                int quantity = stoi(tokens[3]);
                string merchant = session.currentUser->getUsername();
                productManager.updateProduct(name, merchant, description, price, quantity);
                return "200 商品更新成功";
            }
            else if (cmd == "APPLY_DISCOUNT") {
                auto& session = getSession(clientId);
                if (!session.currentUser) throw InputException("请先登录");
                if (session.currentUser->getUserType() != UserType::merchant) {
                    throw InputException("只有商家可以设置折扣");
                }
                // 格式：APPLY_DISCOUNT categoryNum|rate
                string rest;
                getline(ss, rest);
                if (!rest.empty() && rest[0] == ' ')
                    rest.erase(0, rest.find_first_not_of(" "));
                vector<string> tokens;
                stringstream ts(rest);
                string token;
                while (getline(ts, token, '|')) {
                    tokens.push_back(token);
                }
                if (tokens.size() < 2) {
                    throw InputException("参数不足，设置失败");
                }
                int categoryNum = stoi(tokens[0]);
                double rate = stod(tokens[1]);
                ProductType ptype;
                switch (categoryNum) {
                    case 1: ptype = ProductType::book; break;
                    case 2: ptype = ProductType::food; break;
                    case 3: ptype = ProductType::clothing; break;
                    default: throw InputException("无效的类别编号");
                }
                productManager.applyCategoryDiscount(ptype, rate);
                return "200 折扣设置成功";
            }
            else if (cmd == "VIEW_MY_PRODUCTS") {
                auto& session = getSession(clientId);
                if (!session.currentUser) throw InputException("请先登录");
                if (session.currentUser->getUserType() != UserType::merchant) {
                    throw InputException("只有商家可以查看自己的商品");
                }
                string merchant = session.currentUser->getUsername();
                const auto& products = productManager.getAllProducts();
                stringstream result;
                result << "200 ";
                for (const auto& product : products) {
                    if (product->getMerchantUsername() == merchant) {
                        result << product->getName() << "|"
                               << product->getDescription() << "|"
                               << product->getPrice() << "|"
                               << product->getRemainingQuantity() << "|"
                               << product->getCategoryString() << ";";
                    }
                }
                return result.str();
            }

            // —— 购物车 & 订单 —— //

            else if (cmd == "ADD_TO_CART") {
                auto& session = getSession(clientId);
                if (!session.currentUser) throw InputException("请先登录");
                if (session.currentUser->getUserType() != UserType::consumer) {
                    throw InputException("只有消费者可以加入购物车");
                }
                // 格式：ADD_TO_CART name|qty，且要求商品名称必须完全匹配
                string rest;
                getline(ss, rest);
                if (!rest.empty() && rest[0] == ' ')
                    rest.erase(0, rest.find_first_not_of(" "));
                size_t sep = rest.find('|');
                if (sep == string::npos) {
                    throw InputException("参数格式错误");
                }
                string name = rest.substr(0, sep);
                int qty = stoi(rest.substr(sep + 1));
                // 不再用模糊搜索，而是要求完全匹配：
                Product* product = nullptr;
                for (const auto& uptr : productManager.getAllProducts()) {
                    Product* p = uptr.get();
                    if (p->getName() == name) {
                        product = p;
                        break;
                    }
                }
                if (!product) {
                    throw InputException("未找到商品");
                }
                if (product->getRemainingQuantity() < qty) {
                    throw OrderException("商品库存不足");
                }
                session.cart.addItem(product, qty);
                return "200 商品已加入购物车";
            }
            else if (cmd == "VIEW_CART") {
                auto& session = getSession(clientId);
                if (!session.currentUser) throw InputException("请先登录");
                if (session.currentUser->getUserType() != UserType::consumer) {
                    throw InputException("只有消费者可以查看购物车");
                }
                const auto& items = session.cart.getItems();
                stringstream result;
                result << "200 ";
                for (const auto& [product, qty] : items) {
                    result << product->getName() << "|" << qty << ";";
                }
                return result.str();
            }
            else if (cmd == "UPDATE_CART") {
                auto& session = getSession(clientId);
                if (!session.currentUser) throw InputException("请先登录");
                if (session.currentUser->getUserType() != UserType::consumer) {
                    throw InputException("只有消费者可以更新购物车");
                }
                // 格式：UPDATE_CART name|newQty（名称必须完全匹配）
                string rest;
                getline(ss, rest);
                if (!rest.empty() && rest[0] == ' ')
                    rest.erase(0, rest.find_first_not_of(" "));
                size_t sep = rest.find('|');
                if (sep == string::npos) {
                    throw InputException("参数格式错误");
                }
                string name = rest.substr(0, sep);
                int newQty = stoi(rest.substr(sep + 1));
                Product* product = nullptr;
                for (const auto& uptr : productManager.getAllProducts()) {
                    Product* p = uptr.get();
                    if (p->getName() == name) {
                        product = p;
                        break;
                    }
                }
                if (!product) {
                    throw InputException("未找到商品");
                }
                session.cart.updateQuantity(product, newQty);
                return "200 购物车已更新";
            }
            else if (cmd == "CREATE_ORDER") {
                auto& session = getSession(clientId);
                if (!session.currentUser) throw InputException("请先登录");
                if (session.currentUser->getUserType() != UserType::consumer) {
                    throw InputException("只有消费者可以创建订单");
                }
                const auto& cartItems = session.cart.getItems();
                if (cartItems.empty()) throw InputException("购物车为空");

                // 支持两种用法：
                // 1) 直接 "CREATE_ORDER"，表示用购物车中 **全部** 商品创建订单；
                // 2) "CREATE_ORDER name1,name2,..."，只用购物车中指定名称的商品创建订单。
                string rest;
                getline(ss, rest);
                map<Product*, int> orderItems;

                if (rest.empty() || rest == " ") {
                    // 下单全部：先把购物车里所有商品都放进 orderItems
                    for (auto& [prodPtr, qty] : cartItems) {
                        orderItems[prodPtr] = qty;
                    }
                } else {
                    // “部分下单”分支：按逗号拆出要下单的名称列表
                    if (rest[0] == ' ')
                        rest.erase(0, rest.find_first_not_of(" "));
                    vector<string> desiredNames;
                    string temp;
                    stringstream nameSS(rest);
                    while (getline(nameSS, temp, ',')) {
                        if (!temp.empty())
                            desiredNames.push_back(temp);
                    }
                    if (desiredNames.empty()) {
                        throw InputException("未指定要下单的商品名称");
                    }
                    // 从购物车中找名称匹配的，放到 orderItems
                    for (auto& [prodPtr, qty] : cartItems) {
                        for (auto& wantName : desiredNames) {
                            if (prodPtr->getName() == wantName) {
                                orderItems[prodPtr] = qty;
                            }
                        }
                    }
                    if (orderItems.empty()) {
                        throw InputException("购物车中未找到指定商品");
                    }
                }

                // 校验库存并冻结库存
                auto orderPtr = make_unique<Order>(orderItems);
                // 订单的 total 存在 Order::total 里（原始价格计算），但后续支付时会动态使用最新折扣价
                double totalOriginal = orderPtr->getTotal();
                session.orders.push_back(move(orderPtr));

                // 从购物车中移除已下单的商品
                if (!rest.empty() && rest != " ") {
                    // “部分下单”：遍历 orderItems，按 orderItems 中记录的 qty 去删除购物车里对应那几个产品
                    for (auto& [prodPtr, qty] : orderItems) {
                        session.cart.removeItem(prodPtr, qty);
                    }
                } else {
                    // “全部下单”：一次性清空
                    session.cart.clear();
                }

                // 返回原始金额提示
                return "200 订单创建成功 总金额:" + to_string(totalOriginal);
            }
            else if (cmd == "VIEW_ORDERS") {
                auto& session = getSession(clientId);
                if (!session.currentUser) throw InputException("请先登录");
                if (session.currentUser->getUserType() != UserType::consumer) {
                    throw InputException("只有消费者可以查看订单");
                }
                const auto& orders = session.orders;
                stringstream result;
                result << "200 ";
                for (const auto& orderPtr : orders) {
                    // 动态按当前折扣重新计算总金额
                    double recalculatedTotal = 0.0;
                    for (const auto& [product, qty] : orderPtr->getItems()) {
                        recalculatedTotal += product->getPrice() * qty;
                    }
                    result << orderPtr->getId() << "|"
                           << recalculatedTotal << "|"
                           << (orderPtr->isPaid() ? "已支付" :
                               (orderPtr->isCancelled() ? "已取消" : "待支付")) << ";";
                }
                return result.str();
            }
            else if (cmd == "PAY_ORDER") {
                auto& session = getSession(clientId);
                if (!session.currentUser) throw InputException("请先登录");
                if (session.currentUser->getUserType() != UserType::consumer) {
                    throw InputException("只有消费者可以支付订单");
                }
                int orderId;
                ss >> orderId;
                for (auto& orderPtr : session.orders) {
                    if (orderPtr->getId() == orderId) {
                        if (orderPtr->isPaid()) throw OrderException("订单已支付");
                        if (orderPtr->isCancelled()) throw OrderException("订单已取消");
                        map<string, double> merchantAmounts;
                        double totalAmount = 0;
                        for (const auto& [product, qty] : orderPtr->getItems()) {
                            string merchantUser = product->getMerchantUsername();
                            // 按当前折扣价计算应付金额
                            double amt = product->getPrice() * qty;
                            merchantAmounts[merchantUser] += amt;
                            totalAmount += amt;
                        }
                        if (session.currentUser->getBalance() < totalAmount) {
                            throw OrderException("余额不足，无法支付该订单");
                        }
                        for (const auto& [merchantUser, amt] : merchantAmounts) {
                            User* merchant = userManager.getMerchant(merchantUser);
                            session.currentUser->consume(amt);
                            merchant->recharge(amt);
                        }
                        orderPtr->pay();
                        userManager.saveUsers();
                        productManager.saveProducts();
                        return "200 支付成功 订单号:" + to_string(orderId);
                    }
                }
                throw OrderException("订单不存在");
            }
            else if (cmd == "CANCEL_ORDER") {
                auto& session = getSession(clientId);
                if (!session.currentUser) throw InputException("请先登录");
                if (session.currentUser->getUserType() != UserType::consumer) {
                    throw InputException("只有消费者可以取消订单");
                }
                int orderId;
                ss >> orderId;
                for (auto& orderPtr : session.orders) {
                    if (orderPtr->getId() == orderId) {
                        if (orderPtr->isPaid()) throw OrderException("已支付订单不能取消");
                        orderPtr->cancel();
                        return "200 订单取消成功 订单号:" + to_string(orderId);
                    }
                }
                throw OrderException("订单不存在");
            }
            else if (cmd == "LOGOUT") {
                // ===== 修改：登出时清除未支付订单并恢复库存，同时清除当前会话的 currentUser =====
                auto& session = getSession(clientId);
                for (auto& orderPtr : session.orders) {
                    if (!orderPtr->isPaid() && !orderPtr->isCancelled()) {
                        orderPtr->cancel();
                    }
                }
                session.currentUser = nullptr;  // 清除登录标记
                return "200 退出成功";
            }

            // —— 未知命令 —— //
            else {
                return "400 未知命令";
            }
        }
        catch (const exception& e) {
            return "500 " + string(e.what());
        }
    }

    // 在新线程中执行的函数：循环读取客户端发送的命令，调用 processCommand 并回写结果
    void handleClient(SOCKET client_socket) {
        int clientId = static_cast<int>(client_socket);
        char buffer[2048];

        while (true) {
            memset(buffer, 0, sizeof(buffer));
            int bytes_read = recv(client_socket, buffer, sizeof(buffer) - 1, 0);
            if (bytes_read <= 0) {
                break;  // 客户端断开或出错
            }
            string command(buffer, bytes_read);
            // 处理指令
            string response = processCommand(clientId, command);
            send(client_socket, response.c_str(), static_cast<int>(response.size()), 0);
        }

        // 客户端断开，清理会话
        {
            CriticalSectionGuard guard(&sessionCs);
            sessions.erase(clientId);
        }
        closesocket(client_socket);
        cout << "客户端断开连接: " << clientId << endl;
    }

public:
    ECommerceServer(const string& userFile, const string& productFile)
        : userManager(userFile), productManager(productFile) {
        InitializeCriticalSection(&sessionCs);
    }

    ~ECommerceServer() {
        DeleteCriticalSection(&sessionCs);
    }

    void start(int port) {
        // 初始化 Winsock
        WSADATA wsaData;
        if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
            cerr << "Winsock 初始化失败: " << WSAGetLastError() << endl;
            return;
        }

        SOCKET server_fd = socket(AF_INET, SOCK_STREAM, 0);
        if (server_fd == INVALID_SOCKET) {
            cerr << "创建 socket 失败: " << WSAGetLastError() << endl;
            WSACleanup();
            return;
        }

        sockaddr_in address;
        address.sin_family = AF_INET;
        address.sin_addr.s_addr = INADDR_ANY;
        address.sin_port = htons(port);

        if (bind(server_fd, reinterpret_cast<sockaddr*>(&address), sizeof(address)) == SOCKET_ERROR) {
            cerr << "绑定端口失败: " << WSAGetLastError() << endl;
            closesocket(server_fd);
            WSACleanup();
            return;
        }

        if (listen(server_fd, SOMAXCONN) == SOCKET_ERROR) {
            cerr << "监听失败: " << WSAGetLastError() << endl;
            closesocket(server_fd);
            WSACleanup();
            return;
        }

        cout << "服务器启动，端口: " << port << endl;

        while (true) {
            sockaddr_in client_addr;
            int addr_len = sizeof(client_addr);
            SOCKET client_socket = accept(server_fd, reinterpret_cast<sockaddr*>(&client_addr), &addr_len);
            if (client_socket == INVALID_SOCKET) {
                cerr << "接受连接失败: " << WSAGetLastError() << endl;
                continue;
            }

            int clientId = static_cast<int>(client_socket);
            {
                CriticalSectionGuard guard(&sessionCs);
                sessions[clientId] = ClientSession();
            }

            // 为每个新客户端创建一个线程
            ThreadParam* param = new ThreadParam{ this, client_socket };
            HANDLE threadHandle = CreateThread(
                nullptr, 0, ClientThreadProc, param, 0, nullptr
            );
            if (!threadHandle) {
                cerr << "创建线程失败: " << GetLastError() << endl;
                delete param;
                closesocket(client_socket);
            } else {
                CloseHandle(threadHandle);
            }
        }

        closesocket(server_fd);
        WSACleanup();
    }
};

int main() {
    // 修复编码问题
    EncodingFixer::fixConsoleEncoding();
    EncodingFixer::printWelcome();

    CHAR buf[MAX_PATH];
    DWORD len = GetModuleFileNameA(NULL, buf, MAX_PATH);
    if (len == 0 || len == MAX_PATH) {
        cerr << "无法获取可执行路径" << endl;
        return 1;
    }
    string fullPath(buf, len);
    size_t pos = fullPath.find_last_of("\\/");
    string exeDir = (pos == string::npos ? "." : fullPath.substr(0, pos));

    string userFile    = exeDir + "\\users3.txt";
    string productFile = exeDir + "\\products3.txt";

    try {
        ECommerceServer server(userFile, productFile);
        cout << ">>> 用户文件路径:    " << userFile << endl;
        cout << ">>> 商品文件路径:    " << productFile << endl;
        cout << ">>> 服务器启动中..." << endl;
        server.start(8080);
    } catch (const exception& e) {
        cerr << "服务器错误: " << e.what() << endl;
        cout << "按任意键退出..." << endl;
        cin.get();
        return 1;
    }
    return 0;
}
