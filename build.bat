@echo off
chcp 65001 > nul
echo 正在编译电商平台...

echo.
echo 编译服务器端...
g++ -std=c++17 -O2 -Wall -o server.exe server.cpp -lws2_32
if %errorlevel% neq 0 (
    echo 服务器编译失败！
    pause
    exit /b 1
)
echo 服务器编译成功！

echo.
echo 编译客户端...
g++ -std=c++17 -O2 -Wall -o client.exe client.cpp -lws2_32
if %errorlevel% neq 0 (
    echo 客户端编译失败！
    pause
    exit /b 1
)
echo 客户端编译成功！

echo.
echo 编译完成！
echo 使用方法：
echo 1. 先运行 server.exe 启动服务器
echo 2. 再运行 client.exe 启动客户端
echo.
pause
