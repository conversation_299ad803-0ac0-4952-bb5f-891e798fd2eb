{"files.associations": {"string": "cpp", "limits": "cpp", "vector": "cpp", "ostream": "cpp", "istream": "cpp", "iostream": "cpp", "map": "cpp", "algorithm": "cpp", "memory": "cpp", "fstream": "cpp", "sstream": "cpp", "stdexcept": "cpp", "iosfwd": "cpp", "format": "cpp", "array": "cpp", "string_view": "cpp", "span": "cpp", "initializer_list": "cpp", "optional": "cpp", "variant": "cpp", "atomic": "cpp", "deque": "cpp", "unordered_map": "cpp", "text_encoding": "cpp", "typeinfo": "cpp", "system_error": "cpp", "new": "cpp", "bit": "cpp", "cctype": "cpp", "charconv": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "exception": "cpp", "functional": "cpp", "iterator": "cpp", "memory_resource": "cpp", "numeric": "cpp", "random": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "numbers": "cpp", "streambuf": "cpp"}}