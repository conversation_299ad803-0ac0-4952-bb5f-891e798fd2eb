#ifndef FIX_ENCODING_H
#define FIX_ENCODING_H

#include <windows.h>
#include <iostream>
#include <locale>
#include <io.h>
#include <fcntl.h>

class EncodingFixer {
public:
    static void fixConsoleEncoding() {
        // 设置控制台代码页为UTF-8
        SetConsoleCP(65001);
        SetConsoleOutputCP(65001);
        
        // 设置C运行时库的本地化
        setlocale(LC_ALL, "zh_CN.UTF-8");
        
        // 设置C++流的本地化
        std::locale::global(std::locale("zh_CN.UTF-8"));
        std::wcout.imbue(std::locale("zh_CN.UTF-8"));
        std::wcin.imbue(std::locale("zh_CN.UTF-8"));
        
        // 设置控制台字体
        CONSOLE_FONT_INFOEX cfi;
        cfi.cbSize = sizeof(cfi);
        cfi.nFont = 0;
        cfi.dwFontSize.X = 0;
        cfi.dwFontSize.Y = 16;
        cfi.FontFamily = FF_DONTCARE;
        cfi.FontWeight = FW_NORMAL;
        wcscpy_s(cfi.FaceName, L"Consolas");
        SetCurrentConsoleFontEx(GetStdHandle(STD_OUTPUT_HANDLE), FALSE, &cfi);
        
        // 启用ANSI转义序列支持（Windows 10+）
        HANDLE hOut = GetStdHandle(STD_OUTPUT_HANDLE);
        DWORD dwMode = 0;
        GetConsoleMode(hOut, &dwMode);
        dwMode |= ENABLE_VIRTUAL_TERMINAL_PROCESSING;
        SetConsoleMode(hOut, dwMode);
    }
    
    static void printWelcome() {
        std::cout << "==================================" << std::endl;
        std::cout << "    电商平台系统 v1.0" << std::endl;
        std::cout << "    编码已优化，支持中文显示" << std::endl;
        std::cout << "==================================" << std::endl;
        std::cout << std::endl;
    }
};

#endif // FIX_ENCODING_H
